import { Chat<PERSON><PERSON>AI } from '@langchain/openai';
import { PromptTemplate } from '@langchain/core/prompts';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { StateGraph, START, END } from '@langchain/langgraph';
import { config, defaultAgentConfigs } from '../config';
import { NodeExecutionResult, AgentError } from '../types';
import { logger, PerformanceLogger } from '../utils/logger';

// 定义Agent B的状态接口
interface AgentBState {
  input: string;
  session_id: string;
  current_iteration: number;
  max_iterations: number;
  iteration_results: string[];
  current_result?: string;
  should_continue: boolean;
  final_result?: string;
  total_tokens: number;
  error?: string;
  metadata?: {
    execution_time?: number;
    tokens_used?: number;
    model_used?: string;
    iteration_count?: number;
  };
}

export class AgentB {
  private model: ChatOpenAI;
  private promptTemplate: PromptTemplate;
  private iterationPromptTemplate: PromptTemplate;
  private config: any;
  private workflow: any;

  constructor() {
    this.config = defaultAgentConfigs.agent_b;

    // 初始化 ChatOpenAI 模型
    this.model = new ChatOpenAI({
      openAIApiKey: config.openai.apiKey,
      modelName: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: 60000,
    });

    // 初始执行的提示词模板
    this.promptTemplate = PromptTemplate.fromTemplate(`
${this.config.systemPrompt}

用户输入: {input}

这是你的初次分析。请提供全面的回答，但要考虑到你可能需要进行后续的迭代优化。

分析要求：
1. 提供初步的深入分析
2. 识别可能需要进一步探讨的方面
3. 给出明确的结论和建议
4. 为后续迭代留下改进空间

当前迭代: 第1次
初步分析：
`);

    // 迭代优化的提示词模板
    this.iterationPromptTemplate = PromptTemplate.fromTemplate(`
${this.config.systemPrompt}

原始用户输入: {input}
当前迭代次数: 第{iteration}次
上一次的分析结果: {previous_result}

基于上一次的分析，请进行进一步的思考和优化。考虑以下方面：
1. 是否有遗漏的重要观点？
2. 分析的深度是否足够？
3. 结论是否需要调整？
4. 是否有新的见解可以补充？

优化后的分析：
`);

    // 初始化 StateGraph 工作流
    this.workflow = this.createWorkflow();

    logger.info('Agent B initialized with StateGraph iteration capability', {
      model: this.config.model,
      temperature: this.config.temperature
    });
  }

  private createWorkflow(): any {
    try {
      // 定义状态配置 - 参考Agent A的实现，使用channels包装
      const stateConfig = {
        channels: {
          input: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          session_id: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          current_iteration: {
            value: (x: any, y: any) => y ?? x ?? 0,
            default: () => 0,
          },
          max_iterations: {
            value: (x: any, y: any) => y ?? x ?? 3,
            default: () => 3,
          },
          iteration_results: {
            value: (x: any, y: any) => y ?? x ?? [],
            default: () => [],
          },
          current_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          should_continue: {
            value: (x: any, y: any) => y ?? x ?? true,
            default: () => true,
          },
          final_result: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          total_tokens: {
            value: (x: any, y: any) => y ?? x ?? 0,
            default: () => 0,
          },
          error: {
            value: (x: any, y: any) => y ?? x ?? "",
            default: () => "",
          },
          metadata: {
            value: (x: any, y: any) => ({ ...x, ...y }),
            default: () => ({}),
          },
        }
      };

      // 创建StateGraph - 使用类型断言绕过复杂的类型检查
      const workflow = new StateGraph(stateConfig as any)
        .addNode("initial_analysis", this.initialAnalysisNode.bind(this) as any)
        .addNode("iterative_analysis", this.iterativeAnalysisNode.bind(this) as any)
        .addNode("continue_check", this.continueCheckNode.bind(this) as any)
        .addNode("finalize_result", this.finalizeResultNode.bind(this) as any)
        .addEdge(START, "initial_analysis")
        .addEdge("initial_analysis", "continue_check")
        .addConditionalEdges(
          "continue_check",
          this.shouldContinueRouting.bind(this) as any,
          {
            continue: "iterative_analysis",
            finalize: "finalize_result"
          }
        )
        .addEdge("iterative_analysis", "continue_check")
        .addEdge("finalize_result", END)
        .compile();

      logger.debug('LangGraph workflow initialized for Agent B');
      return workflow;

    } catch (error: any) {
      logger.error('Failed to initialize LangGraph workflow for Agent B', { error: error.message });
      throw new AgentError('Workflow initialization failed', 'WORKFLOW_INIT_FAILED', 'agent_b');
    }
  }

  async execute(input: string, sessionId: string, currentIteration: number = 0): Promise<NodeExecutionResult> {
    try {
      logger.debug('Agent B starting StateGraph execution', {
        session_id: sessionId,
        input_length: input.length,
        current_iteration: currentIteration
      });

      // 验证输入
      if (!input || typeof input !== 'string' || input.trim().length === 0) {
        throw new AgentError('Input cannot be empty', 'INVALID_INPUT', 'agent_b');
      }

      // 使用LangGraph StateGraph执行
      const state: AgentBState = {
        input,
        session_id: sessionId,
        current_iteration: currentIteration,
        max_iterations: 3,
        iteration_results: [],
        should_continue: true,
        total_tokens: 0
      };

      const result = await this.workflow.invoke(state);

      if (result.error) {
        throw new AgentError(result.error, 'WORKFLOW_EXECUTION_FAILED', 'agent_b');
      }

      return {
        success: true,
        result: result.final_result || '',
        execution_time: result.metadata?.execution_time || 0,
        tokens_used: result.metadata?.tokens_used || 0
      };

    } catch (error: any) {
      logger.error('Agent B StateGraph execution failed', {
        session_id: sessionId,
        error: error.message,
        stack: error.stack
      });

      if (error instanceof AgentError) {
        throw error;
      }

      throw new AgentError(
        `Agent B execution failed: ${error.message}`,
        'EXECUTION_FAILED',
        'agent_b',
        { originalError: error.message }
      );
    }
  }

  // StateGraph 节点函数
  private async initialAnalysisNode(state: AgentBState): Promise<Partial<AgentBState>> {
    const perfLogger = new PerformanceLogger(`agent_b_initial_${state.session_id}`);

    try {
      logger.debug('Agent B initial analysis node', {
        session_id: state.session_id,
        iteration: state.current_iteration
      });

      const prompt = await this.promptTemplate.format({ input: state.input });
      perfLogger.checkpoint('initial_prompt_formatted');

      const response = await this.model.invoke(prompt);
      perfLogger.checkpoint('initial_model_invoked');

      const result = this.processResponse(response.content);
      const tokens = this.estimateTokens(state.input, result);

      const executionTime = perfLogger.end();

      return {
        current_result: result,
        iteration_results: [result],
        current_iteration: 1,
        total_tokens: state.total_tokens + tokens,
        metadata: {
          execution_time: executionTime,
          tokens_used: tokens,
          model_used: this.config.model,
          iteration_count: 1
        }
      };

    } catch (error: any) {
      logger.error('Initial analysis node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return {
        error: `Initial analysis failed: ${error.message}`,
        should_continue: false
      };
    }
  }

  private async iterativeAnalysisNode(state: AgentBState): Promise<Partial<AgentBState>> {
    const perfLogger = new PerformanceLogger(`agent_b_iteration_${state.session_id}_${state.current_iteration}`);

    try {
      logger.debug('Agent B iterative analysis node', {
        session_id: state.session_id,
        iteration: state.current_iteration
      });

      // 获取上一次的结果
      const previousResult = state.iteration_results[state.iteration_results.length - 1] || '';

      const prompt = await this.iterationPromptTemplate.format({
        input: state.input,
        iteration: state.current_iteration + 1,
        previous_result: previousResult
      });

      perfLogger.checkpoint(`iteration_${state.current_iteration}_prompt_formatted`);

      const response = await this.model.invoke(prompt);
      perfLogger.checkpoint(`iteration_${state.current_iteration}_model_invoked`);

      const result = this.processResponse(response.content);
      const tokens = this.estimateTokens(state.input, result);

      const executionTime = perfLogger.end();

      return {
        current_result: result,
        iteration_results: [...state.iteration_results, result],
        current_iteration: state.current_iteration + 1,
        total_tokens: state.total_tokens + tokens,
        metadata: {
          ...state.metadata,
          execution_time: (state.metadata?.execution_time || 0) + executionTime,
          tokens_used: (state.metadata?.tokens_used || 0) + tokens,
          iteration_count: state.current_iteration + 1
        }
      };

    } catch (error: any) {
      logger.error('Iterative analysis node failed', {
        session_id: state.session_id,
        iteration: state.current_iteration,
        error: error.message
      });

      return {
        error: `Iterative analysis failed: ${error.message}`,
        should_continue: false
      };
    }
  }

  private async continueCheckNode(state: AgentBState): Promise<Partial<AgentBState>> {
    try {
      logger.debug('Agent B continue check node', {
        session_id: state.session_id,
        iteration: state.current_iteration,
        max_iterations: state.max_iterations
      });

      // 检查是否达到最大迭代次数
      if (state.current_iteration >= state.max_iterations) {
        return { should_continue: false };
      }

      // 基于当前结果判断是否需要继续迭代
      const currentResult = state.current_result || '';

      // 如果结果太短，可能需要更深入的分析
      if (currentResult.length < 200) {
        return { should_continue: true };
      }

      // 基于关键词判断是否需要进一步分析
      const analysisKeywords = ['需要进一步', '可以深入', '待补充', '有待完善'];
      const hasAnalysisHints = analysisKeywords.some(keyword => currentResult.includes(keyword));

      return { should_continue: hasAnalysisHints };

    } catch (error: any) {
      logger.error('Continue check node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return { should_continue: false };
    }
  }

  private async finalizeResultNode(state: AgentBState): Promise<Partial<AgentBState>> {
    try {
      logger.debug('Agent B finalize result node', {
        session_id: state.session_id,
        total_iterations: state.current_iteration
      });

      let finalResult: string;

      if (state.iteration_results.length === 1) {
        // 只有一次迭代
        finalResult = this.formatFinalResult(state.iteration_results[0], 1);
      } else {
        // 多次迭代，合并结果
        finalResult = this.combineIterationResults(state.iteration_results, state.current_iteration);
      }

      return {
        final_result: finalResult,
        should_continue: false
      };

    } catch (error: any) {
      logger.error('Finalize result node failed', {
        session_id: state.session_id,
        error: error.message
      });

      return {
        error: `Finalize result failed: ${error.message}`,
        final_result: state.current_result || ''
      };
    }
  }

  private shouldContinueRouting(state: AgentBState): string {
    return state.should_continue ? 'continue' : 'finalize';
  }

  private combineIterationResults(results: string[], totalIterations: number): string {
    let combined = `=== Agent B 多轮分析结果 (共${totalIterations}轮) ===\n\n`;

    results.forEach((result, index) => {
      combined += `【第${index + 1}轮分析】\n${result}\n\n`;
    });

    combined += '=== 分析完成 ===';
    return combined;
  }

  private formatFinalResult(result: string, totalIterations: number): string {
    return `=== Agent B 最终分析结果 (共${totalIterations}轮迭代) ===

${result}

=== 迭代分析完成 ===`;
  }

  private processResponse(content: any): string {
    if (typeof content === 'string') {
      return content.trim();
    }
    
    if (content && typeof content === 'object') {
      return JSON.stringify(content);
    }
    
    return String(content || '').trim();
  }

  private estimateTokens(input: string, output: string): number {
    const inputTokens = Math.ceil(input.length / 4);
    const outputTokens = Math.ceil(output.length / 4);
    return inputTokens + outputTokens;
  }

  // 健康检查方法
  async healthCheck(): Promise<boolean> {
    try {
      // 简化健康检查，直接测试模型调用而不是完整的workflow
      const response = await this.model.invoke("请回复'Agent B健康检查通过'");
      const isHealthy = response && response.content !== null;

      logger.debug('Agent B health check completed', {
        success: isHealthy
      });

      return isHealthy;
    } catch (error: any) {
      logger.warn('Agent B health check failed', { error: error.message });
      return false;
    }
  }

  // 获取配置信息
  getConfig(): any {
    return {
      name: this.config.name,
      description: this.config.description,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      supports_iteration: true,
      max_iterations: 3,
      implementation: 'StateGraph',
      workflow_nodes: ['initial_analysis', 'iterative_analysis', 'continue_check', 'finalize_result']
    };
  }
}
